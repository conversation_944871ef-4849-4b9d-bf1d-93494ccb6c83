/**
 * 首页数据路由
 * 提供不同角色的首页统计数据
 */

const express = require('express');
const router = express.Router();

const { database, ContractModel, UserModel } = require('../utils/database');
const { authenticateToken } = require('../middleware/auth');
const { ResponseUtils, DateUtils, DateTimeUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, USER_ROLES, CONTRACT_STATUS, ACTIONS, RESOURCE_TYPES } = require('../utils/constants');
// 缓存相关导入已移除，系统直接从数据库获取数据
const { logOperation } = require('../utils/logger');
const SystemInfo = require('../utils/systemInfo');

/**
 * 获取首页统计数据
 * GET /api/dashboard/stats
 */
router.get('/stats',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      let stats = {};

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          stats = await getEmployeeStats(req.user.id);
          break;
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          stats = await getReviewerStats(req.user.id);
          break;
        case USER_ROLES.LEGAL_OFFICER:
          stats = await getLegalOfficerStats(req.user.id);
          break;
        case USER_ROLES.ADMIN:
          stats = await getAdminStats();
          break;
        default:
          stats = {};
      }

      res.json(ResponseUtils.success(stats));

    } catch (error) {
      console.error('获取首页统计数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取最近活动
 * GET /api/dashboard/activities
 */
router.get('/activities',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const limit = parseInt(req.query.limit) || 10;
      let activities = [];

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          activities = await getEmployeeActivities(req.user.id, limit);
          break;
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          activities = await getReviewerActivities(req.user.id, limit);
          break;
        case USER_ROLES.LEGAL_OFFICER:
          activities = await getLegalOfficerActivities(req.user.id, limit);
          break;
        case USER_ROLES.ADMIN:
          activities = await getAdminActivities(limit);
          break;
      }

      res.json(ResponseUtils.success(activities));

    } catch (error) {
      console.error('获取最近活动错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取快捷操作
 * GET /api/dashboard/quick-actions
 */
router.get('/quick-actions',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      let quickActions = [];

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          quickActions = getEmployeeQuickActions();
          break;
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          quickActions = getReviewerQuickActions();
          break;
        case USER_ROLES.LEGAL_OFFICER:
          quickActions = getLegalOfficerQuickActions();
          break;
        case USER_ROLES.ADMIN:
          quickActions = getAdminQuickActions();
          break;
      }

      res.json(ResponseUtils.success(quickActions));

    } catch (error) {
      console.error('获取快捷操作错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取通知信息
 * GET /api/dashboard/notifications
 */
router.get('/notifications',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      let notifications = [];

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          notifications = await getEmployeeNotifications(req.user.id);
          break;
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          notifications = await getReviewerNotifications(req.user.id);
          break;
        case USER_ROLES.LEGAL_OFFICER:
          notifications = await getLegalOfficerNotifications(req.user.id);
          break;
        case USER_ROLES.ADMIN:
          notifications = await getAdminNotifications();
          break;
      }

      res.json(ResponseUtils.success(notifications));

    } catch (error) {
      console.error('获取通知信息错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 获取用户个人统计数据
 * GET /api/dashboard/user-stats
 */
router.get('/user-stats',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const userId = req.user.id;
      let userStats = {};

      switch (userRole) {
        case USER_ROLES.EMPLOYEE:
          userStats = await getUserEmployeeStats(userId);
          break;
        case USER_ROLES.COUNTY_REVIEWER:
        case USER_ROLES.CITY_REVIEWER:
          userStats = await getUserReviewerStats(userId);
          break;
        case USER_ROLES.LEGAL_OFFICER:
          userStats = await getUserLegalOfficerStats(userId);
          break;
        case USER_ROLES.ADMIN:
          userStats = await getUserAdminStats();
          break;
        default:
          userStats = {};
      }

      res.json(ResponseUtils.success(userStats));

    } catch (error) {
      console.error('获取用户个人统计数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

// ==================== 员工相关统计 ====================

/**
 * 获取员工统计数据
 */
async function getEmployeeStats(userId) {
  // 获取合同统计
  const contractStats = await ContractModel.getStats({ submitter_id: userId });

  // 获取本月提交数量
  const thisMonthCount = await getThisMonthSubmissions(userId);

  // 获取平均审核时间
  const avgReviewTime = await getAverageReviewTime(userId);

  return {
    contracts: contractStats,
    thisMonth: thisMonthCount,
    averageReviewTime: avgReviewTime,
    summary: {
      totalSubmitted: contractStats.total,
      pendingReview: contractStats.pending, // reviewing已经合并到pending中
      approved: contractStats.approved,
      rejected: contractStats.rejected
    }
  };
}

/**
 * 获取员工最近活动
 */
async function getEmployeeActivities(userId, limit) {
  const contracts = await ContractModel.getList(
    { submitter_id: userId },
    1,
    limit
  );

  return contracts.data.map(contract => ({
    id: contract.id,
    type: 'contract',
    action: getContractAction(contract.status),
    title: `合同 ${contract.serial_number}`,
    description: contract.filename,
    status: contract.status,
    time: contract.updated_at || contract.created_at
  }));
}

// ==================== 审核员相关统计 ====================

/**
 * 获取审核员统计数据
 */
async function getReviewerStats(userId) {
  // 使用基于审核历史的统计方法
  const contractStats = await ContractModel.getReviewerStats(userId);

  // 获取待审核数量
  const pendingCount = await getPendingReviewCount(userId);

  // 获取本月审核数量
  const thisMonthReviewed = await getThisMonthReviewed(userId);

  // 获取平均审核时间
  const avgReviewTime = await getReviewerAverageTime(userId);

  // 计算总的已完成审核数量
  const totalCompleted = contractStats.approved + contractStats.rejected;

  return {
    contracts: contractStats,
    pending: pendingCount,
    thisMonthReviewed: thisMonthReviewed,
    averageReviewTime: avgReviewTime,
    summary: {
      totalAssigned: contractStats.pending + contractStats.pending_city_review + totalCompleted,
      pendingReview: contractStats.pending + contractStats.pending_city_review,
      completed: totalCompleted
    }
  };
}

/**
 * 获取审核员最近活动
 */
async function getReviewerActivities(userId, limit) {
  const contracts = await ContractModel.getList(
    { reviewer_id: userId },
    1,
    limit
  );

  return contracts.data.map(contract => ({
    id: contract.id,
    type: 'review',
    action: getReviewAction(contract.status),
    title: `审核合同 ${contract.serial_number}`,
    description: `提交人: ${contract.submitter_name}`,
    status: contract.status,
    time: contract.reviewed_at || contract.updated_at || contract.created_at
  }));
}

// ==================== 管理员相关统计 ====================

/**
 * 获取管理员统计数据
 */
async function getAdminStats() {
  // 获取全部合同统计
  const contractStats = await ContractModel.getStats();

  // 获取用户统计
  const userStats = await getUserStats();

  // 获取本月统计
  const thisMonthStats = await getThisMonthStats();

  // 获取系统概览
  const systemOverview = await getSystemOverview();

  return {
    contracts: contractStats,
    users: userStats,
    thisMonth: thisMonthStats,
    system: systemOverview,
    summary: {
      totalContracts: contractStats.total,
      totalUsers: userStats.total,
      activeUsers: userStats.active,
      systemHealth: 'good'
    }
  };
}

/**
 * 获取管理员最近活动
 */
async function getAdminActivities(limit) {
  const contracts = await ContractModel.getList({}, 1, limit);

  return contracts.data.map(contract => ({
    id: contract.id,
    type: 'system',
    action: getSystemAction(contract.status),
    title: `合同 ${contract.serial_number}`,
    description: `${contract.submitter_name} → ${contract.reviewer_name}`,
    status: contract.status,
    time: contract.updated_at || contract.created_at
  }));
}

// ==================== 快捷操作定义 ====================

function getEmployeeQuickActions() {
  return [
    {
      key: 'submit-contract',
      title: '提交合同',
      description: '上传新的合同文件进行审核',
      icon: 'Upload',
      color: 'primary',
      path: '/submit'
    },
    {
      key: 'my-contracts',
      title: '我的合同',
      description: '查看我提交的所有合同',
      icon: 'Document',
      color: 'success',
      path: '/my-contracts'
    },
    {
      key: 'profile',
      title: '个人设置',
      description: '修改个人信息和密码',
      icon: 'User',
      color: 'info',
      path: '/profile'
    }
  ];
}

function getReviewerQuickActions() {
  return [
    {
      key: 'pending-review',
      title: '待审核',
      description: '查看分配给我的待审核合同',
      icon: 'Timer',
      color: 'warning',
      path: '/pending-review'
    },

    {
      key: 'reviewed',
      title: '已审核',
      description: '查看已完成审核的合同',
      icon: 'Check',
      color: 'success',
      path: '/reviewed'
    }
  ];
}

function getAdminQuickActions() {
  return [
    {
      key: 'user-management',
      title: '用户管理',
      description: '管理系统用户和权限',
      icon: 'UserFilled',
      color: 'primary',
      path: '/user-management'
    },
    {
      key: 'contract-management',
      title: '合同管理',
      description: '查看和管理所有合同',
      icon: 'FolderOpened',
      color: 'success',
      path: '/contract-management'
    },
    {
      key: 'system-stats',
      title: '系统统计',
      description: '查看系统使用统计',
      icon: 'DataBoard',
      color: 'info',
      path: '/system-stats'
    }
  ];
}

// ==================== 辅助函数 ====================

async function getThisMonthSubmissions(userId) {
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);

  // 这里简化实现，实际应该使用 SQL 查询
  const contracts = await ContractModel.getList({ submitter_id: userId }, 1, 1000);
  return contracts.data.filter(contract =>
    new Date(contract.created_at) >= startOfMonth
  ).length;
}

async function getAverageReviewTime(userId) {
  try {
    // 查询已完成审核的合同，计算平均审核时间
    const sql = `
      SELECT
        AVG(JULIANDAY(reviewed_at) - JULIANDAY(created_at)) as avg_days
      FROM contracts
      WHERE reviewer_id = ?
        AND status IN ('approved', 'rejected')
        AND reviewed_at IS NOT NULL
        AND created_at IS NOT NULL
    `;

    const result = await database.get(sql, [userId]);

    if (result && result.avg_days !== null) {
      const avgDays = Math.round(result.avg_days * 10) / 10; // 保留一位小数
      return `${avgDays}天`;
    }

    return '暂无数据';
  } catch (error) {
    console.error('计算平均审核时间失败:', error);
    return '暂无数据';
  }
}

async function getPendingReviewCount(userId) {
  const result = await ContractModel.getStats({
    reviewer_id: userId,
    status: CONTRACT_STATUS.PENDING
  });
  return result.pending || 0;
}

async function getThisMonthReviewed(userId) {
  try {
    // 获取本月开始时间（UTC）
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfMonthUTC = startOfMonth.toISOString();

    // 查询本月审核的合同数量
    const sql = `
      SELECT COUNT(*) as count
      FROM contracts
      WHERE reviewer_id = ?
        AND status IN ('approved', 'rejected')
        AND reviewed_at >= ?
        AND reviewed_at IS NOT NULL
    `;

    const result = await database.get(sql, [userId, startOfMonthUTC]);
    return result ? result.count : 0;
  } catch (error) {
    console.error('获取本月审核数量失败:', error);
    return 0;
  }
}

async function getReviewerAverageTime(userId) {
  // 简化实现，返回模拟数据
  return '1.8天';
}

async function getUserStats() {
  const users = await UserModel.getList({}, 1, 1000);
  const total = users.pagination.total;
  const active = users.data.filter(user => user.status === 'active').length;

  return {
    total,
    active,
    employees: users.data.filter(user => user.role === USER_ROLES.EMPLOYEE).length,
    reviewers: users.data.filter(user =>
      user.role === USER_ROLES.COUNTY_REVIEWER || user.role === USER_ROLES.CITY_REVIEWER
    ).length,
    admins: users.data.filter(user => user.role === USER_ROLES.ADMIN).length
  };
}

async function getThisMonthStats() {
  // 简化实现，返回模拟数据
  return {
    contracts: 25,
    users: 3,
    reviews: 20
  };
}

async function getSystemOverview() {
  try {
    const systemInfo = SystemInfo.getSystemInfo();
    return {
      uptime: systemInfo.uptime,
      version: systemInfo.version,
      lastBackup: systemInfo.lastBackup,
      diskUsage: systemInfo.diskUsage,
      memory: systemInfo.memory,
      platform: systemInfo.platform,
      nodeVersion: systemInfo.nodeVersion
    };
  } catch (error) {
    console.error('获取系统概览失败:', error);
    // 降级到默认值
    return {
      uptime: '未知',
      version: '1.0.0',
      lastBackup: '未知',
      diskUsage: '未知'
    };
  }
}

async function getEmployeeNotifications(userId) {
  // 简化实现，返回模拟通知
  return [
    {
      id: 1,
      type: 'info',
      title: '合同审核完成',
      message: '您的合同 HT001 已审核通过',
      time: DateTimeUtils.nowUTC(),
      read: false
    }
  ];
}

async function getReviewerNotifications(userId) {
  const pendingCount = await getPendingReviewCount(userId);
  const notifications = [];

  if (pendingCount > 0) {
    notifications.push({
      id: 1,
      type: 'warning',
      title: '待审核合同',
      message: `您有 ${pendingCount} 个合同待审核`,
      time: new Date().toISOString(),
      read: false
    });
  }

  return notifications;
}

async function getAdminNotifications() {
  // 简化实现，返回模拟通知
  return [
    {
      id: 1,
      type: 'success',
      title: '系统运行正常',
      message: '所有服务运行正常',
      time: new Date().toISOString(),
      read: true
    }
  ];
}

function getContractAction(status) {
  const actions = {
    [CONTRACT_STATUS.PENDING]: '已提交',
    [CONTRACT_STATUS.PENDING_CITY_REVIEW]: '待市局审核',
    [CONTRACT_STATUS.PENDING_CONTRACT_NUMBER]: '待分配编号',
    [CONTRACT_STATUS.APPROVED]: '已通过',
    [CONTRACT_STATUS.COMPLETED]: '已完成',
    [CONTRACT_STATUS.REJECTED]: '已拒绝'
  };
  return actions[status] || '未知';
}

function getReviewAction(status) {
  const actions = {
    [CONTRACT_STATUS.PENDING]: '待审核',
    [CONTRACT_STATUS.PENDING_CITY_REVIEW]: '待市局审核',
    [CONTRACT_STATUS.PENDING_CONTRACT_NUMBER]: '待分配编号',
    [CONTRACT_STATUS.APPROVED]: '审核通过',
    [CONTRACT_STATUS.COMPLETED]: '已完成',
    [CONTRACT_STATUS.REJECTED]: '审核拒绝'
  };
  return actions[status] || '未知';
}

function getSystemAction(status) {
  const actions = {
    [CONTRACT_STATUS.PENDING]: '新合同提交',
    [CONTRACT_STATUS.APPROVED]: '合同审核通过',
    [CONTRACT_STATUS.REJECTED]: '合同审核拒绝'
  };
  return actions[status] || '系统活动';
}

// ==================== 用户个人统计函数 ====================

/**
 * 获取员工个人统计数据
 */
async function getUserEmployeeStats(userId) {
  const contractStats = await ContractModel.getStats({ submitter_id: userId });

  return {
    totalContracts: contractStats.total || 0,
    pendingContracts: contractStats.pending || 0,
    approvedContracts: contractStats.approved || 0,
    rejectedContracts: contractStats.rejected || 0,
    totalReviewed: 0,
    pendingReview: 0,
    approvedReview: 0,
    rejectedReview: 0
  };
}

/**
 * 获取审核员个人统计数据
 */
async function getUserReviewerStats(userId) {
  // 使用基于审核历史的统计方法
  const contractStats = await ContractModel.getReviewerStats(userId);

  // 计算总的已完成审核数量
  const totalCompleted = contractStats.approved + contractStats.rejected;

  return {
    totalContracts: 0,
    pendingContracts: 0,
    approvedContracts: 0,
    rejectedContracts: 0,
    totalReviewed: totalCompleted,
    pendingReview: contractStats.pending + contractStats.pending_city_review,
    approvedReview: contractStats.approved || 0,
    rejectedReview: contractStats.rejected || 0
  };
}

/**
 * 获取管理员个人统计数据
 */
async function getUserAdminStats() {
  const contractStats = await ContractModel.getStats({});

  return {
    totalContracts: contractStats.total || 0,
    pendingContracts: contractStats.pending || 0,
    approvedContracts: contractStats.approved || 0,
    rejectedContracts: contractStats.rejected || 0,
    totalReviewed: 0,
    pendingReview: 0,
    approvedReview: 0,
    rejectedReview: 0
  };
}

/**
 * 清理dashboard缓存（已简化）
 * POST /api/dashboard/clear-cache
 */
router.post('/clear-cache',
  authenticateToken,
  async (req, res) => {
    try {
      const userId = req.user.id;
      const userRole = req.user.role;

      res.json(ResponseUtils.success({
        message: '缓存清理成功（系统直接从数据库获取数据）',
        clearedType: 'none',
        userId: userId,
        userRole: userRole,
        note: 'Cache system removed, data fetched directly from database'
      }));

    } catch (error) {
      console.error('Dashboard缓存清理接口错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  });

/**
 * 清理指定用户的dashboard缓存（用于用户切换）
 * POST /api/dashboard/clear-user-cache/:userId
 */
router.post('/clear-user-cache/:userId',
  authenticateToken,
  async (req, res) => {
    try {
      const targetUserId = parseInt(req.params.userId);
      const currentUserId = req.user.id;
      const currentUserRole = req.user.role;

      // 验证权限：只允许清理自己的缓存或管理员清理任意用户缓存
      if (targetUserId !== currentUserId && currentUserRole !== 'admin') {
        return res.status(403).json(ResponseUtils.error('无权限清理其他用户缓存'));
      }

      // 缓存系统已移除，数据直接从数据库获取

      // 记录操作日志
      await logOperation({
        userId: currentUserId,
        action: ACTIONS.DELETE,
        resourceType: RESOURCE_TYPES.SYSTEM,
        resourceId: `user_cache_${targetUserId}`,
        details: JSON.stringify({
          action: 'clear_user_cache',
          targetUserId: targetUserId,
          requestUserId: currentUserId
        }),
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        status: 'success'
      });

      res.json(ResponseUtils.success(null, `用户 ${targetUserId} 的缓存清理成功`));
    } catch (error) {
      console.error('清理用户缓存失败:', error);
      res.status(500).json(ResponseUtils.error('清理用户缓存失败'));
    }
  }
);

// ==================== 法规员统计函数 ====================

/**
 * 获取法规员统计数据
 */
async function getLegalOfficerStats(userId) {
  try {
    // 法规员主要关注待分配合同编号的合同数量
    const pendingNumberContracts = await database.all(`
      SELECT COUNT(*) as count
      FROM contracts
      WHERE status = ?
    `, [CONTRACT_STATUS.PENDING_CONTRACT_NUMBER]);

    // 已完成分配编号的合同数量
    const completedContracts = await database.all(`
      SELECT COUNT(*) as count
      FROM contracts
      WHERE status = ? AND contract_number IS NOT NULL
    `, [CONTRACT_STATUS.COMPLETED]);

    return {
      summary: {
        pendingAssignment: pendingNumberContracts[0]?.count || 0,
        completedAssignment: completedContracts[0]?.count || 0,
        totalProcessed: (pendingNumberContracts[0]?.count || 0) + (completedContracts[0]?.count || 0)
      },
      message: '法规员专注于合同编号分配，不提供详细工作统计'
    };
  } catch (error) {
    console.error('获取法规员统计数据失败:', error);
    return {
      summary: {
        pendingAssignment: 0,
        completedAssignment: 0,
        totalProcessed: 0
      },
      message: '法规员专注于合同编号分配，不提供详细工作统计'
    };
  }
}

/**
 * 获取法规员活动记录
 */
async function getLegalOfficerActivities(userId, limit = 10) {
  try {
    // 法规员的活动主要是合同编号分配相关
    const activities = await database.all(`
      SELECT
        'contract_number_assigned' as type,
        '分配合同编号' as action,
        contract_number as target,
        updated_at as timestamp,
        '已为合同分配编号: ' || contract_number as description
      FROM contracts
      WHERE status = ? AND contract_number IS NOT NULL
      ORDER BY updated_at DESC
      LIMIT ?
    `, [CONTRACT_STATUS.COMPLETED, limit]);

    return activities.map(activity => ({
      ...activity,
      timestamp: new Date(activity.timestamp).toISOString()
    }));
  } catch (error) {
    console.error('获取法规员活动记录失败:', error);
    return [];
  }
}

/**
 * 获取法规员快捷操作
 */
function getLegalOfficerQuickActions() {
  return [];
}

/**
 * 获取法规员通知
 */
async function getLegalOfficerNotifications(userId) {
  try {
    // 法规员主要关注新的待分配编号的合同
    const pendingCount = await database.get(`
      SELECT COUNT(*) as count
      FROM contracts
      WHERE status = ?
    `, [CONTRACT_STATUS.PENDING_CONTRACT_NUMBER]);

    const notifications = [];

    if (pendingCount && pendingCount.count > 0) {
      notifications.push({
        id: 'pending_number_assignment',
        type: 'info',
        title: '待分配合同编号',
        message: `有 ${pendingCount.count} 个合同等待分配编号`,
        timestamp: new Date().toISOString(),
        read: false
      });
    }

    return notifications;
  } catch (error) {
    console.error('获取法规员通知失败:', error);
    return [];
  }
}

/**
 * 获取法规员用户统计数据
 */
async function getUserLegalOfficerStats(userId) {
  try {
    // 法规员不提供详细的工作统计，只提供基本信息
    return {
      role: '市局法规员',
      description: '负责为通过审核的合同分配合同编号',
      message: '法规员专注于合同编号分配工作，不提供详细统计数据',
      pendingAssignment: 0,
      completedAssignment: 0
    };
  } catch (error) {
    console.error('获取法规员用户统计失败:', error);
    return {
      role: '市局法规员',
      description: '负责为通过审核的合同分配合同编号',
      message: '法规员专注于合同编号分配工作，不提供详细统计数据',
      pendingAssignment: 0,
      completedAssignment: 0
    };
  }
}

module.exports = router;
